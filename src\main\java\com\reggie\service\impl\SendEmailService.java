package com.reggie.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
  import org.springframework.beans.factory.annotation.Value;
  import org.springframework.mail.MailException;
  import org.springframework.mail.SimpleMailMessage;
 import org.springframework.mail.javamail.JavaMailSenderImpl;
  import org.springframework.stereotype.Service;
//import reggie_take_out.common.customException;

@Service
  public class SendEmailService {
  @Autowired
  private JavaMailSenderImpl mailSender;
  @Value("${spring.mail.username}")
  private String from;
  public void sendSimpleEmail(String to, String subject, String code) {
         // 定制纯文本邮件信息
         SimpleMailMessage message = new SimpleMailMessage();
         message.setFrom(from);
         message.setTo(to);
         message.setSubject(subject);
         message.setText("验证码为"+code);
         try {
             // 发送邮件
             mailSender.send(message);
             System.out.println("纯文本邮件发送成功");
             } catch (MailException e) {
             System.out.println("纯文本邮件发送失败 " + e.getMessage());
             e.printStackTrace();

         }
         }
 }