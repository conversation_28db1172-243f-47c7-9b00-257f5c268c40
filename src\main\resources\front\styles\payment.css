.payment-container {
    padding: 20px;
    background-color: #f8f8f8;
    min-height: 100vh;
}

.payment-header {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.order-number {
    font-size: 14px;
    color: #666;
}

.order-amount {
    font-size: 24px;
    color: #ff6b35;
    font-weight: bold;
}

.payment-methods {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.payment-method-title {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    font-weight: bold;
}

.qr-code-section {
    padding: 20px;
    text-align: center;
}

.qr-code {
    width: 200px;
    height: 200px;
    border: 2px solid #ddd;
    border-radius: 8px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    position: relative;
}

.qr-code::before {
    content: "虚拟支付二维码";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.qr-tip {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.password-section {
    padding: 0 20px 20px;
}

.password-input-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.password-label {
    width: 80px;
    font-size: 14px;
    color: #333;
}

.password-input {
    flex: 1;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 12px;
    font-size: 16px;
}

.password-input:focus {
    border-color: #ff6b35;
    outline: none;
}

.password-tip {
    font-size: 12px;
    color: #999;
    margin-left: 80px;
}

.payment-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 15px 20px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

.pay-button {
    width: 100%;
    height: 50px;
    background: #ff6b35;
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.pay-button:hover {
    background: #e55a2b;
}

.pay-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error-message {
    color: #ff4757;
    font-size: 14px;
    margin-top: 10px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .payment-container {
        padding: 10px;
    }
    
    .qr-code {
        width: 150px;
        height: 150px;
    }
    
    .order-amount {
        font-size: 20px;
    }
}
