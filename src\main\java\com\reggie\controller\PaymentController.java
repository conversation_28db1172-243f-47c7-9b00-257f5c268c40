package com.reggie.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.reggie.common.BaseContext;
import com.reggie.common.R;
import com.reggie.entity.Orders;
import com.reggie.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 支付控制器
 */
@Slf4j
@RestController
@RequestMapping("/payment")
public class PaymentController {

    @Autowired
    private OrderService orderService;

    /**
     * 验证支付密码并完成支付
     * @param orderId 订单ID
     * @param password 支付密码
     * @return
     */
    @PostMapping("/pay")
    public R<String> pay(@RequestParam Long orderId, @RequestParam String password) {
        log.info("支付请求：订单ID={}, 密码={}", orderId, password);
        
        // 验证支付密码
        if (!"1111".equals(password)) {
            return R.error("支付密码错误");
        }
        
        // 查询订单是否存在且属于当前用户
        Orders order = orderService.getById(orderId);
        if (order == null) {
            return R.error("订单不存在");
        }
        
        if (!order.getUserId().equals(BaseContext.getCurrentId())) {
            return R.error("无权限操作此订单");
        }
        
        if (order.getStatus() != 1) {
            return R.error("订单状态异常，无法支付");
        }
        
        // 更新订单状态为待派送，并设置支付时间
        LambdaUpdateWrapper<Orders> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Orders::getId, orderId)
                    .set(Orders::getStatus, 2)  // 待派送
                    .set(Orders::getCheckoutTime, LocalDateTime.now());
        
        boolean result = orderService.update(updateWrapper);
        
        if (result) {
            return R.success("支付成功");
        } else {
            return R.error("支付失败，请重试");
        }
    }

    /**
     * 获取订单支付信息
     * @param orderId 订单ID
     * @return
     */
    @GetMapping("/info/{orderId}")
    public R<Orders> getPaymentInfo(@PathVariable Long orderId) {
        log.info("获取支付信息：订单ID={}", orderId);
        
        Orders order = orderService.getById(orderId);
        if (order == null) {
            return R.error("订单不存在");
        }
        
        if (!order.getUserId().equals(BaseContext.getCurrentId())) {
            return R.error("无权限查看此订单");
        }
        
        return R.success(order);
    }
}
