<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fa6b831b-43c1-49ec-9c9f-8beabaae9123" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="XML Properties File" />
        <option value="properties File" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\Edata\work\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../env/apache-maven-3.5.2" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="D:\Edata\work\apache-maven-3.5.2\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2y1RmOKegzAuCesL5jLxct7DIO2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/springboot/1/reggie&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;onboarding.tips.debug.path&quot;: &quot;E:/广州理工学院/2024-2025年第二学期/高级架构技术/project/reggie/src/main/java/org/example/Main.java&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;3acf5c7b740d6e2538f3a7b88cf069b3&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;com.intellij.ide.scratch.ScratchImplUtil$2/New Scratch File&quot;: [
      &quot;yaml&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\广州理工学院\2024-2025年第二学期\高级架构技术\project\reggie\src\main\java\com\reggie" />
      <recent name="E:\广州理工学院\2024-2025年第二学期\高级架构技术\project\reggie\src\main\resources" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\springboot\reggie\src\main\resources" />
      <recent name="D:\springboot\reggie\src\main\java\com\reggie\service\impl" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="ReggieApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="reggie" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.reggie.ReggieApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.reggie.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ReggieApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fa6b831b-43c1-49ec-9c9f-8beabaae9123" name="Changes" comment="" />
      <created>1749000251419</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749000251419</updated>
      <workItem from="1749000252763" duration="3601000" />
      <workItem from="1749696578728" duration="951000" />
      <workItem from="1749697586230" duration="2193000" />
      <workItem from="1749720590691" duration="1544000" />
      <workItem from="1749732344098" duration="631000" />
      <workItem from="1749774546162" duration="8588000" />
      <workItem from="1749786442027" duration="114000" />
      <workItem from="1749786616952" duration="686000" />
      <workItem from="1749794873228" duration="118000" />
      <workItem from="1749798306679" duration="3394000" />
      <workItem from="1749809724503" duration="1280000" />
      <workItem from="1749811019883" duration="698000" />
      <workItem from="1749811986172" duration="269000" />
      <workItem from="1749812343232" duration="739000" />
      <workItem from="1749813652530" duration="616000" />
      <workItem from="1749814277547" duration="289000" />
      <workItem from="1749814575177" duration="136000" />
      <workItem from="1749814722011" duration="68000" />
      <workItem from="1749814852261" duration="99000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/example/Main.java</url>
          <line>15</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>