<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
    <title>支付 - 菩提阁</title>
    <link rel="icon" href="./../images/favico.ico">
    <!--不同屏幕尺寸根字体设置-->
    <script src="./../js/base.js"></script>
    <!--element-ui的样式-->
    <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
    <!-- 引入样式  -->
    <link rel="stylesheet" href="../styles/index.css" />
    <!--本页面内容的样式-->
    <link rel="stylesheet" href="./../styles/payment.css" />
</head>
<body>
    <div id="payment" class="app">
        <div class="divHead">
            <div class="divTitle">
                <i class="el-icon-arrow-left" @click="goBack"></i>
                支付订单
                <img src="./../images/home.png" @click="toMainPage"/>
            </div>
        </div>
        
        <div class="payment-container">
            <!-- 订单信息 -->
            <div class="payment-header">
                <div class="order-info">
                    <div class="order-number">订单号：{{orderInfo.number}}</div>
                    <div class="order-amount">￥{{orderInfo.amount}}</div>
                </div>
            </div>
            
            <!-- 支付方式 -->
            <div class="payment-methods">
                <div class="payment-method-title">支付方式</div>
                
                <!-- 二维码支付 -->
                <div class="qr-code-section">
                    <div class="qr-code"></div>
                    <div class="qr-tip">请使用手机扫描上方二维码完成支付</div>
                </div>
                
                <!-- 密码支付 -->
                <div class="password-section">
                    <div class="password-input-group">
                        <label class="password-label">支付密码：</label>
                        <input 
                            type="password" 
                            class="password-input" 
                            v-model="paymentPassword"
                            placeholder="请输入支付密码"
                            maxlength="4"
                            @keyup.enter="submitPayment"
                        />
                    </div>
                    <div class="password-tip">提示：支付密码为 1111</div>
                </div>
            </div>
            
            <!-- 错误信息 -->
            <div v-if="errorMessage" class="error-message">
                {{errorMessage}}
            </div>
        </div>
        
        <!-- 支付按钮 -->
        <div class="payment-actions">
            <button 
                class="pay-button" 
                @click="submitPayment"
                :disabled="loading || !paymentPassword"
            >
                <span v-if="loading" class="loading"></span>
                {{loading ? '支付中...' : '确认支付'}}
            </button>
        </div>
    </div>
    
    <!-- 开发环境版本，包含了有帮助的命令行警告 -->
    <script src="../../backend/plugins/vue/vue.js"></script>
    <!-- 引入组件库 -->
    <script src="../../backend/plugins/element-ui/index.js"></script>
    <!-- 引入axios -->
    <script src="../../backend/plugins/axios/axios.min.js"></script>
    <script src="./../js/request.js"></script>
    <script src="./../api/payment.js"></script>
    <script>
    new Vue({
        el: "#payment",
        data() {
            return {
                orderId: null,
                orderInfo: {},
                paymentPassword: '',
                loading: false,
                errorMessage: ''
            }
        },
        computed: {},
        created() {
            this.getOrderId();
            this.loadOrderInfo();
        },
        mounted() {},
        methods: {
            // 获取URL参数中的订单ID
            getOrderId() {
                const urlParams = new URLSearchParams(window.location.search);
                this.orderId = urlParams.get('orderId');
                if (!this.orderId) {
                    this.$notify({ type: 'error', message: '订单ID不存在' });
                    this.goBack();
                }
            },
            
            // 加载订单信息
            async loadOrderInfo() {
                if (!this.orderId) return;
                
                try {
                    const res = await getPaymentInfoApi(this.orderId);
                    if (res.code === 1) {
                        this.orderInfo = res.data;
                        // 检查订单状态
                        if (this.orderInfo.status !== 1) {
                            this.$notify({ type: 'warning', message: '订单状态异常，无法支付' });
                            this.goBack();
                        }
                    } else {
                        this.$notify({ type: 'error', message: res.msg || '获取订单信息失败' });
                        this.goBack();
                    }
                } catch (error) {
                    console.error('获取订单信息失败:', error);
                    this.$notify({ type: 'error', message: '网络错误，请重试' });
                }
            },
            
            // 提交支付
            async submitPayment() {
                if (!this.paymentPassword) {
                    this.errorMessage = '请输入支付密码';
                    return;
                }
                
                if (this.loading) return;
                
                this.loading = true;
                this.errorMessage = '';
                
                try {
                    const res = await submitPaymentApi({
                        orderId: this.orderId,
                        password: this.paymentPassword
                    });
                    
                    if (res.code === 1) {
                        this.$notify({ type: 'success', message: '支付成功' });
                        // 跳转到支付成功页面
                        window.requestAnimationFrame(() => {
                            window.location.replace('/front/page/pay-success.html');
                        });
                    } else {
                        this.errorMessage = res.msg || '支付失败';
                    }
                } catch (error) {
                    console.error('支付失败:', error);
                    this.errorMessage = '网络错误，请重试';
                } finally {
                    this.loading = false;
                }
            },
            
            // 返回上一页
            goBack() {
                window.requestAnimationFrame(() => {
                    window.history.back();
                });
            },
            
            // 回到首页
            toMainPage() {
                window.requestAnimationFrame(() => {
                    window.location.replace('/front/index.html');
                });
            }
        }
    })
    </script>
</body>
</html>
