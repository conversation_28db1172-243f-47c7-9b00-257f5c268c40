<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=no,minimal-ui">
        <title>菩提阁</title>
        <link rel="icon" href="./../images/favico.ico">
        <!--不同屏幕尺寸根字体设置-->
        <script src="./../js/base.js"></script>
        <!--element-ui的样式-->
        <link rel="stylesheet" href="../../backend/plugins/element-ui/index.css" />
        <!-- 引入样式  -->
        <link rel="stylesheet" href="../styles/index.css" />
        <!--本页面内容的样式-->
        <link rel="stylesheet" href="./../styles/no-wify.css" />
    </head>
    <body>
        <div id="no_wifi" class="app">
            <div class="divHead">
                <div class="divTitle">
                    <i class="el-icon-arrow-left" @click="goBack"></i>菩提阁
                </div>
            </div>
            <div class="divContent">
                <img src="./../images//no_wifi.png"/>
                <div class="divDesc">网络连接异常</div>
                <div class="btnRefresh" @click="toPagePrev">点击刷新</div>
            </div>
        </div>
            <!-- 开发环境版本，包含了有帮助的命令行警告 -->
            <script src="../../backend/plugins/vue/vue.js"></script>
            <!-- 引入组件库 -->
            <script src="../../backend/plugins/element-ui/index.js"></script>
            <!-- 引入axios -->
            <script src="../../backend/plugins/axios/axios.min.js"></script>
            <script src="./../js/request.js"></script>
            <script>
            new Vue({
                el:"#no_wifi",
                data(){
                    return {
                    }
                },
                computed:{},
                created(){

                },
                mounted(){},
                methods:{
                    goBack(){
                        history.go(-1)
                    },
                    toPagePrev(){
                        history.go(-1)
                    }
                }
            })
            </script>
    </body>
</html>