#add_order .divHead {
  width: 100%;
  height: 88rem;
  opacity: 1;
  background: #333333;
  position: relative;
}

#add_order .divHead .divTitle {
  font-size: 18rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 25rem;
  letter-spacing: 0;
  position: absolute;
  bottom: 13rem;
  width: 100%;
}

#add_order .divHead .divTitle i {
  position: absolute;
  left: 16rem;
  top: 50%;
  transform: translate(0, -50%);
}

#add_order .divContent {
  margin: 10rem 10rem 0 10rem;
  height: calc(100vh - 56rem - 110rem);
  overflow-y: auto;
}

#add_order .divContent .divAddress {
  height: 120rem;
  opacity: 1;
  background: #ffffff;
  border-radius: 6rem;
  position: relative;
  padding: 11rem 10rem 0 16rem;
}

#add_order .divContent .divAddress .address {
  height: 25rem;
  opacity: 1;
  font-size: 18rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: left;
  color: #20232a;
  line-height: 25rem;
  margin-bottom: 4rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 300rem;
}

#add_order .divContent .divAddress .name {
  height: 17rem;
  opacity: 1;
  font-size: 12rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  line-height: 17rem;
}

#add_order .divContent .divAddress .name span:first-child {
  margin-right: 2rem;
}

#add_order .divContent .divAddress i {
  position: absolute;
  right: 14rem;
  top: 32rem;
}

#add_order .divContent .divAddress .divSplit {
  width: 100%;
  height: 1px;
  opacity: 1;
  border: 0;
  background-color: #ebebeb;
  margin-top: 14rem;
}

#add_order .divContent .divAddress .divFinishTime {
  height: 47rem;
  opacity: 1;
  font-size: 12rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #333333;
  line-height: 47rem;
  margin-left: 2rem;
}

#add_order .divContent .order {
  background: #ffffff;
  border-radius: 6rem;
  margin-top: 10rem;
  margin-bottom: 10rem;
  padding: 3rem 10rem 7rem 16rem;
}

#add_order .divContent .order .title {
  height: 56rem;
  line-height: 56rem;
  opacity: 1;
  font-size: 16rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: left;
  color: #333333;
  letter-spacing: 0;
}

#add_order .divContent .order .divSplit {
  height: 1px;
  opacity: 1;
  background-color: #efefef;
  border: 0;
}

#add_order .divContent .order .itemList .item {
  display: flex;
}

#add_order .divContent .order .itemList .item .el-image {
  padding-top: 20rem;
  padding-bottom: 20rem;
  width: 64rem;
  height: 64rem;
}

#add_order .divContent .order .itemList .item .el-image img {
  width: 64rem;
  height: 64rem;
}

#add_order .divContent .order .itemList .item:first-child .desc {
  border: 0;
}

#add_order .divContent .order .itemList .item .desc {
  padding-top: 20rem;
  padding-bottom: 20rem;
  border-top: 2px solid #ebeef5;
  width: calc(100% - 64rem);
}

#add_order .divContent .order .itemList .item .desc .name {
  height: 22rem;
  opacity: 1;
  font-size: 16rem;
  font-family: PingFangSC, PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  color: #20232a;
  line-height: 22rem;
  letter-spacing: 0;
  margin-left: 10rem;
  margin-bottom: 16rem;
}

#add_order .divContent .order .itemList .item .desc .numPrice {
  height: 22rem;
  display: flex;
  justify-content: space-between;
}

#add_order .divContent .order .itemList .item .desc .numPrice span {
  margin-left: 12rem;
  height: 20rem;
  opacity: 1;
  font-size: 14rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #818693;
  line-height: 20rem;
  letter-spacing: 0;
  display: inline-block;
}

#add_order .divContent .order .itemList .item .desc .numPrice .price {
  font-size: 20rem;
  font-family: DIN, DIN-Medium;
  font-weight: 500;
  text-align: left;
  color: #e94e3c;
}

#add_order
  .divContent
  .order
  .itemList
  .item
  .desc
  .numPrice
  .price
  .spanMoney {
  color: #e94e3c;
  font-size: 12rem;
}

#add_order .divContent .note {
  height: 164rem;
  opacity: 1;
  background: #ffffff;
  border-radius: 6px;
  margin-top: 11rem;
  padding: 3rem 10rem 10rem 11rem;
}

#add_order .divContent .note .title {
  height: 56rem;
  opacity: 1;
  font-size: 16rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: left;
  color: #333333;
  line-height: 56rem;
  letter-spacing: 0px;
  border-bottom: 2rem solid #efefef;
}

#add_order .divContent .note .van-cell {
  height: 103rem;
}

#add_order .divCart {
  width: 345rem;
  height: 44rem;
  opacity: 1;
  background: #000000;
  border-radius: 25rem;
  box-shadow: 0rem 3rem 5rem 0rem rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  margin-top: 10rem;
  z-index: 3000;
  position: absolute;
  /* bottom: 35rem; */
  bottom: 12rem;
  left: 50%;
  transform: translate(-50%, 0);
}

#add_order .divCart .imgCartActive {
  background-image: url("./../images/cart_active.png");
}

#add_order .divCart .imgCart {
  background-image: url("./../images/cart.png");
}

#add_order .divCart > div:first-child {
  width: 60rem;
  height: 60rem;
  position: absolute;
  left: 11rem;
  bottom: 0;
  background-size: 60rem 60rem;
}

#add_order .divCart .divNum {
  font-size: 12rem;
  font-family: DIN, DIN-Medium;
  font-weight: 500;
  text-align: left;
  color: #ffffff;
  letter-spacing: 0rem;
  position: absolute;
  left: 92rem;
  top: 10rem;
}

#add_order .divCart .divNum span:last-child {
  font-size: 20rem;
}

#add_order .divCart > div:last-child {
  width: 102rem;
  height: 36rem;
  opacity: 1;
  border-radius: 18rem;
  font-size: 15rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  text-align: center;
  line-height: 36rem;
  position: absolute;
  right: 5rem;
  top: 4rem;
}

#add_order .divCart .btnSubmit {
  color: white;
  background: #d8d8d8;
}
#add_order .divCart .btnSubmitActive {
  color: #333333;
  background: #ffc200;
}

#add_order .divCart .divGoodsNum {
  width: 18rem;
  height: 18rem;
  opacity: 1;
  background: #e94e3c;
  border-radius: 50%;
  text-align: center;
  font-size: 12rem;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 500;
  color: #ffffff;
  line-height: 18rem;
  position: absolute;
  left: 57rem;
  top: -5rem;
}

#add_order .divCart .moreGoods {
  width: 25rem;
  height: 25rem;
  line-height: 25rem;
}
