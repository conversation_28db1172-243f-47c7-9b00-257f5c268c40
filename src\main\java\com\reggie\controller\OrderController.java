package com.reggie.controller;

import com.reggie.entity.Orders;
import com.reggie.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/submit")
    public Map<String, Object> submit(@RequestBody Orders orders){
        System.out.println("订单数据：" + orders);
        orderService.submit(orders);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("msg", "下单成功");
        result.put("data", "下单成功");
        return result;
    }
}
