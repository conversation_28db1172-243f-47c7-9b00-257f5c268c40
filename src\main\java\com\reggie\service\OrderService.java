package com.reggie.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.reggie.dto.OrdersDto;
import com.reggie.entity.Orders;

/*
    mybatisplus管得很宽，明明是持久层框架，但是他把service也给管了
 */
public interface OrderService extends IService<Orders> {

    Orders submit(Orders orders);

    Page<OrdersDto> pageOrdersAndOrdersDetail(Integer page, Integer pageSize);

}
